import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AvatarDisplay from './AvatarDisplay';

// Profile sidebar component with improved layout and rounded square avatar

interface SidebarProps {
  firstName: string | null | undefined;
  lastName?: string | null;
  email?: string | null;
  jobTitle?: string | null;
  avatarUrl?: string | null;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  showBusinessMenu?: boolean;
  canAddFunding?: boolean;
  showEventMenu?: boolean;
  unreadNotificationCount?: number;
}

const Sidebar = ({ firstName, lastName, email, jobTitle, avatarUrl, activeTab = 'profile', onTabChange, showBusinessMenu = true, canAddFunding = false, showEventMenu = true, unreadNotificationCount = 0 }: SidebarProps) => {
  // Create full name or fallback to email username
  const getDisplayName = () => {
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    }
    if (firstName) {
      return firstName;
    }
    if (lastName) {
      return lastName;
    }
    return email?.split('@')[0] || 'User';
  };
  
  const displayName = getDisplayName();
  
  const handleTabClick = (tab: string) => {
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  return (
    <Card className="lg:col-span-1">
      <CardHeader className="pb-4">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="flex-shrink-0">
            <AvatarDisplay
              avatarUrl={avatarUrl}
              firstName={firstName}
              lastName={lastName}
              email={email}
              size="xl"
              shape="square"
            />
          </div>
          <div className="min-w-0 w-full max-w-full px-1">
            <CardTitle className="text-lg leading-tight break-words word-wrap truncate-multiline text-center">
              {displayName}
            </CardTitle>
            <CardDescription className="break-words text-sm mt-1 leading-relaxed text-center">
              {jobTitle || 'Member'}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <nav className="space-y-2">
          <div 
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'profile' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('profile')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
            <span>Profile</span>
          </div>
          
          <div
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'notifications' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('notifications')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
              <path d="M13.73 21a2 2 0 0 1-3.46 0" />
            </svg>
            <span>Notifications</span>
            {unreadNotificationCount > 0 && (
              <Badge variant="destructive" className="ml-auto h-5 w-5 p-0 flex items-center justify-center text-xs">
                {unreadNotificationCount > 99 ? '99+' : unreadNotificationCount}
              </Badge>
            )}
          </div>

          <div
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'connections' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('connections')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M16 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            <span>Connections</span>
          </div>
          
          <div
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'account' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('account')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19.69 14a6.9 6.9 0 0 0 .31-2V5l-8-3-3.16 1.18" />
              <path d="M4 14.32V5l8-3v7" />
              <path d="M9.78 21h10.44a2 2 0 0 0 1.8-2.89l-2.94-5.89a2 2 0 0 0-1.8-1.11h-4.56a2 2 0 0 0-1.8 1.11l-2.94 5.89A2 2 0 0 0 9.78 21Z" />
              <path d="M15 12v6" />
            </svg>
            <span>Account Settings</span>
          </div>

          <div
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'subscriptions' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('subscriptions')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <line x1="2" x2="22" y1="10" y2="10" />
            </svg>
            <span>Subscriptions</span>
          </div>

          {canAddFunding && (
            <div
              className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'funding' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
              onClick={() => handleTabClick('funding')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10" />
                <path d="M16 8l-4 4-4-4" />
                <path d="M12 12v6" />
              </svg>
              <span>My Funding</span>
            </div>
          )}

          {showBusinessMenu && (
            <div
              className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'business' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
              onClick={() => handleTabClick('business')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M6 21V5a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v16" />
                <path d="M3 21h18" />
                <path d="M10 9h4" />
                <path d="M10 13h4" />
                <path d="M10 17h4" />
              </svg>
              <span>Businesses</span>
            </div>
          )}

          {showEventMenu && (
            <div
              className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'events' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
              onClick={() => handleTabClick('events')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M8 2v4" />
                <path d="M16 2v4" />
                <rect width="18" height="18" x="3" y="4" rx="2" />
                <path d="M3 10h18" />
              </svg>
              <span>Events</span>
            </div>
          )}

          <div
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'feedback' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('feedback')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
            </svg>
            <span>Platform Feedback</span>
          </div>
        </nav>
      </CardContent>
    </Card>
  );
};

export default Sidebar;
