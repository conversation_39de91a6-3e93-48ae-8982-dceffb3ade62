import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Briefcase, Calendar, Users, GraduationCap, MessageCircle, PoundSterling, Book, Network, Scale, Globe } from "lucide-react";
import { VideoText } from "@/components/magicui/video-text";
import { StatsCounters } from "@/components/StatsCounters";
import { useAuth } from "@/contexts/AuthContext";
import { useAnalytics } from "@/services/analyticsService";
import { useToast } from "@/hooks/use-toast";
import { useEffect } from "react";

// Custom color palette - Forest Green theme with dark-to-light gradient
const colorPalette = {
  funding: "bg-[#1B4332] text-white hover:bg-[#1F4A39]", // Deep Forest Green
  business: "bg-[#2D5016] text-white hover:bg-[#335A19]", // Dark Forest Green
  events: "bg-[#40531B] text-white hover:bg-[#495E1F]", // Olive Forest Green
  professionals: "bg-[#4A7C59] text-white hover:bg-[#558E66]", // Medium Forest Green
  jobs: "bg-[#52796F] text-white hover:bg-[#5E8A80]", // Teal Forest Green
  education: "bg-[#6B8E23] text-white hover:bg-[#7AA329]", // Sage Green
  social: "bg-[#7FB069] text-white hover:bg-[#91C677]", // Light Sage Green
};

const HomePage = () => {
  const { user } = useAuth();
  const { trackPageView, trackDirectoryView } = useAnalytics();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    trackPageView('/home', 'Home Page');

    // Log the current URL for debugging
    console.log('🏠 HomePage mounted with URL:', window.location.href);
  }, [trackPageView]);

  // Directory order: Row 1 (Primary Actions), Row 2 (Community & Learning), Row 3 (Social Hub)
  const directories = [
    {
      title: "Funding Finder",
      description: "Discover grants, investments, and financial opportunities for sustainable initiatives.",
      icon: PoundSterling,
      path: "/funding",
      color: colorPalette.funding,
    },
    {
      title: "Net Zero Businesses",
      description: "Discover businesses committed to achieving net zero emissions.",
      icon: Briefcase,
      path: "/business-directory",
      color: colorPalette.business,
    },
    {
      title: "Net Zero Events",
      description: "Find conferences, workshops, and events focused on sustainability.",
      icon: Calendar,
      path: "/events",
      color: colorPalette.events,
    },
    {
      title: "Members",
      description: "Connect with experts and professionals in the net zero industry.",
      icon: Users,
      path: "/members",
      color: colorPalette.professionals,
    },
    {
      title: "Net Zero Jobs",
      description: "Explore career opportunities in sustainable organisations.",
      icon: Briefcase,
      path: "/jobs",
      color: colorPalette.jobs,
    },
    {
      title: "Net Zero Education",
      description: "Access courses, training, and educational resources.",
      icon: GraduationCap,
      path: "/education",
      color: colorPalette.education,
    },
  ];

  const socialDirectory = {
    title: "Net Zero Social",
    description: "Connect with the sustainability community, share ideas, and collaborate on climate action projects.",
    icon: MessageCircle,
    path: "/social",
    color: colorPalette.social,
  };

  // Directory link helper
  const getDirectoryLink = (path: string) => (user ? path : "/auth?mode=signup");

  return (
    <div className="max-w-7xl mx-auto px-4 -mt-4">
      {/* Hero Section - Ultra Compact */}
      <section className="py-2 md:py-4 text-center">
        <div className="max-w-xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
            The Complete <span className="text-primary">Net Zero</span> Directory
          </h1>
          <p className="text-base text-gray-600 mb-3">
            Connect with net zero businesses, events, members, and jobs.
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <Button asChild size="sm" className="bg-primary hover:bg-primary-dark">
              <a href="#directories">Explore Directories</a>
            </Button>
            <Button asChild size="sm" variant="outline">
              <a href="#nexzero-meaning">Learn More</a>
            </Button>
          </div>
        </div>
      </section>

      {/* Global Impact Video Text - Ultra Compact */}
      <section className="py-2">
        <div className="relative h-[120px] sm:h-[180px] md:h-[250px] w-full overflow-hidden rounded-lg px-2 sm:px-4">
          <VideoText
            src="/Standard_Mode_Waterfall_in_the_forest_viewing_.mp4"
            fontSize="clamp(2.5rem, 14vw, 7rem)"
            fontWeight="900"
            fontFamily="Arial Black, Impact, sans-serif"
          >
            NeXzero
          </VideoText>
        </div>
        <div className="text-center mt-3">
          <p className="text-base text-gray-600">Working together for a sustainable future</p>
        </div>
      </section>

      {/* Platform Statistics */}
      <StatsCounters />

      {/* NeXzero Context Section - Three containers/row with enhanced styling */}
      <section className="pt-2 pb-8">
        {/* What NeXzero Means - Enhanced with boxes and appropriate icons */}
        <div className="max-w-6xl mx-auto mb-8">
          <div className="text-center mb-8">
            <Book className="mx-auto mb-2 w-8 h-8 text-primary" />
            <h4 id="nexzero-meaning" className="text-2xl font-bold scroll-mt-24">What NeXzero Means</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg border-2 border-gray-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center mb-3">
                <Network className="w-5 h-5 text-primary mr-2" />
                <dt className="font-bold text-lg">Nexus</dt>
              </div>
              <div className="text-sm text-gray-600 mb-2">
                <span className="italic">noun</span> <span className="font-mono">/ˈnɛksəs/</span>
              </div>
              <dd className="text-gray-700">
                <span className="font-semibold">A connection or series of connections</span> linking two or more things; a central point of convergence.
              </dd>
            </div>
            <div className="bg-white p-6 rounded-lg border-2 border-gray-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center mb-3">
                <Scale className="w-5 h-5 text-primary mr-2" />
                <dt className="font-bold text-lg">Net Zero</dt>
              </div>
              <div className="text-sm text-gray-600 mb-2">
                <span className="italic">noun</span> <span className="font-mono">/nɛt ˈzɪərəʊ/</span>
              </div>
              <dd className="text-gray-700">
                <span className="font-semibold">The balance achieved</span> when the amount of greenhouse gases emitted equals the amount removed from the atmosphere.
              </dd>
            </div>
            <div className="bg-white p-6 rounded-lg border-2 border-gray-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center mb-3">
                <Globe className="w-5 h-5 text-primary mr-2" />
                <dt className="font-bold text-lg">NeXzero</dt>
              </div>
              <div className="text-sm text-gray-600 mb-2">
                <span className="italic">noun</span> <span className="font-mono">/nɛksˈzɪərəʊ/</span>
              </div>
              <dd className="text-gray-700">
                <span className="font-semibold">The central connection point</span> for businesses pursuing net zero.
              </dd>
            </div>
          </div>
        </div>

        {/* Three containers in a row */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {/* Who Uses NeXzero - With background shading */}
          <div className="lg:col-span-2 bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-lg border border-gray-200">
            <h4 className="text-lg font-semibold mb-4 flex items-center">
              <Users className="w-5 h-5 text-primary mr-2" />
              Who Uses NeXzero
            </h4>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <div><span className="font-bold">Businesses showcasing solutions:</span> Demonstrate your net zero innovations to potential partners</div>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <div><span className="font-bold">Businesses seeking solutions:</span> Find the right partners across healthcare, education, construction, and beyond</div>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <div><span className="font-bold">Individuals:</span> Discover career opportunities and training in the growing sustainability sector</div>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <div><span className="font-bold">Funding seekers:</span> Access financial support to develop your net zero innovations or implement solutions</div>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-primary rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <div><span className="font-bold">Event networkers:</span> Connect with the community driving net zero transformation</div>
              </li>
            </ul>
          </div>

          {/* NeXzero Platform Description */}
          <div className="bg-white p-6 rounded-lg border-2 border-primary/20 shadow-sm">
            <h4 className="text-lg font-semibold mb-4 flex items-center">
              <Globe className="w-5 h-5 text-primary mr-2" />
              NeXzero Platform
            </h4>
            <p className="text-gray-700 leading-relaxed">
              <strong>NeXzero:</strong> The cross-industry platform where insights are shared, partnerships are formed, and net zero progress accelerates across all sectors.
            </p>
          </div>
        </div>
      </section>

      {/* Directories Section - Reduced vertical spacing, square cards */}
      <section id="directories" className="py-8 scroll-mt-24">
        <h2 className="text-3xl font-bold text-center mb-8">Our Directories</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {directories.map((dir) => (
            <Link key={dir.title} to={getDirectoryLink(dir.path)} className="group">
              <div className="aspect-[4/2.5] rounded-xl overflow-hidden shadow-[0_4px_8px_rgba(0,0,0,0.1)] hover:shadow-xl transition-all duration-300 border-0 group-hover:translate-y-[-4px] flex flex-col">
                {/* Solid colored header with larger icon */}
                <div className={`${dir.color} p-3 flex flex-col items-center justify-center h-2/5 relative transition-colors duration-300`}>
                  <div className="absolute inset-0 opacity-10">
                    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" className="h-full w-full">
                      <path fill="currentColor" d="M42.7,-65.4C56.4,-54.3,69.3,-42.9,75.6,-28.3C81.9,-13.6,81.6,4.3,76.8,20.4C72,36.5,62.8,50.8,49.6,58.9C36.4,67,19.2,68.8,2.6,67.3C-14,65.8,-28,60.9,-41.4,52.8C-54.9,44.6,-67.8,33.1,-74.2,18C-80.6,2.9,-80.4,-15.8,-73.1,-31.5C-65.7,-47.1,-51.2,-59.6,-36.2,-70.5C-21.1,-81.3,-5.3,-90.5,8.6,-87.5C22.5,-84.6,45,-76.6,42.7,-65.4Z" transform="translate(100 100)" />
                    </svg>
                  </div>
                  <dir.icon 
                    size={36} 
                    strokeWidth={1.5} 
                    className="mb-0.5" 
                    stroke="currentColor" 
                  />
                  <h3 className="text-xl font-bold mt-0.5 text-center relative z-10">{dir.title}</h3>
                </div>
                {/* Content area */}
                <div className="px-4 pt-6 pb-4 flex flex-col justify-between flex-grow bg-white">
                  <p className="text-gray-500 text-lg leading-tight flex-grow text-center px-2">{dir.description}</p>
                  <div className="mt-3 group-hover:translate-x-1 transition-transform duration-300">
                    <Button variant="ghost" className="w-full justify-between border border-gray-200 hover:bg-gray-50 hover:border-primary text-base h-8 py-0">
                      <span>Explore {dir.title}</span>
                      <span className="ml-2">→</span>
                    </Button>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Full-width card for Social - Same width as the three cards above */}
        <div className="grid grid-cols-1 gap-6 mb-8">
          <Link to={getDirectoryLink(socialDirectory.path)} className="group">
            <div className="rounded-xl overflow-hidden shadow-[0_4px_8px_rgba(0,0,0,0.1)] hover:shadow-xl transition-all duration-300 border-0 group-hover:translate-y-[-4px] flex flex-row h-32">
              {/* Solid colored section with larger icon */}
              <div className={`${socialDirectory.color} p-3 flex flex-col items-center justify-center w-1/4 sm:w-1/5 relative transition-colors duration-300`}>
                <div className="absolute inset-0 opacity-10">
                  <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" className="h-full w-full">
                    <path fill="currentColor" d="M42.7,-65.4C56.4,-54.3,69.3,-42.9,75.6,-28.3C81.9,-13.6,81.6,4.3,76.8,20.4C72,36.5,62.8,50.8,49.6,58.9C36.4,67,19.2,68.8,2.6,67.3C-14,65.8,-28,60.9,-41.4,52.8C-54.9,44.6,-67.8,33.1,-74.2,18C-80.6,2.9,-80.4,-15.8,-73.1,-31.5C-65.7,-47.1,-51.2,-59.6,-36.2,-70.5C-21.1,-81.3,-5.3,-90.5,8.6,-87.5C22.5,-84.6,45,-76.6,42.7,-65.4Z" transform="translate(100 100)" />
                  </svg>
                </div>
                <socialDirectory.icon size={32} strokeWidth={1.5} className="mb-0.5" stroke="currentColor" />
                <h3 className="text-lg sm:text-xl font-bold text-center relative z-10">{socialDirectory.title}</h3>
              </div>
              {/* Content area */}
              <div className="px-4 pt-6 pb-4 flex flex-col justify-between flex-grow bg-white">
                <div className="flex items-center">
                  <p className="text-gray-500 text-lg leading-tight flex-grow text-left">{socialDirectory.description}</p>
                </div>
                <div className="group-hover:translate-x-1 transition-transform duration-300 self-start mt-2">
                  <Button variant="ghost" className="border border-gray-200 hover:bg-gray-50 hover:border-primary text-base h-8 py-0">
                    <span>Explore Social</span>
                    <span className="ml-2">→</span>
                  </Button>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </section>

      {/* CTA Section - Tighter spacing */}
      <section className="py-8 bg-primary/5 rounded-2xl my-8 p-6 text-center">
        <h2 className="text-2xl md:text-3xl font-bold mb-3">Join the Net Zero Movement</h2>
        <p className="text-lg text-gray-600 mb-5 max-w-2xl mx-auto">
          Whether you're a business, professional, educator, or job-seeker, 
          be part of the solution toward a carbon-neutral future.
        </p>
        <Button asChild size="lg" className="bg-primary hover:bg-primary-dark">
          <Link to="/auth?mode=signup">Get Started Today</Link>
        </Button>
      </section>
    </div>
  );
};

export default HomePage;
