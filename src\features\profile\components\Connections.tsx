import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Users, UserPlus, UserMinus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useConnections } from '@/features/social/hooks/useConnections';
import AvatarDisplay from './AvatarDisplay';
import FollowButton from '@/features/social/components/FollowButton';
import UserProfileLink from '@/features/social/components/UserProfileLink';

const Connections: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'following' | 'followers'>('following');
  
  const {
    connections,
    connectionStats,
    loading,
    error
  } = useConnections(user?.id);

  if (!user) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <p className="text-muted-foreground">Please log in to view connections.</p>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Connections
          </CardTitle>
          <CardDescription>
            Manage your social connections
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gray-200 rounded"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Connections
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-destructive mb-4">{error}</p>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5" />
          Connections
        </CardTitle>
        <CardDescription>
          Manage your social connections and discover new members
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'following' | 'followers')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="following" className="flex items-center gap-2">
              <UserPlus className="w-4 h-4" />
              Following
              <Badge variant="secondary" className="ml-1">
                {connectionStats.following_count}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="followers" className="flex items-center gap-2">
              <UserMinus className="w-4 h-4" />
              Followers
              <Badge variant="secondary" className="ml-1">
                {connectionStats.follower_count}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="following" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">People You Follow</h3>
                <p className="text-sm text-muted-foreground">
                  {connectionStats.following_count} connections
                </p>
              </div>
              
              {connections.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No connections yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Start following other members to see their posts in your feed
                  </p>
                  <Button variant="outline" onClick={() => window.location.href = '/members'}>
                    Discover Members
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {connections.map((connection) => (
                    <div key={connection.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-accent/50 transition-colors">
                      <AvatarDisplay
                        avatarUrl={connection.avatar_url}
                        firstName={connection.first_name}
                        lastName={connection.last_name}
                        email={`${connection.first_name?.toLowerCase() || ''}${connection.last_name?.toLowerCase() || ''}@example.com`}
                        size="md"
                        shape="square"
                      />
                      <div className="flex-1 min-w-0">
                        <UserProfileLink
                          userId={connection.id}
                          firstName={connection.first_name}
                          lastName={connection.last_name}
                          jobTitle={connection.job_title}
                          organisationName={connection.organisation_name}
                          size="sm"
                        />
                      </div>
                      <FollowButton
                        userId={connection.id}
                        variant="outline"
                        size="sm"
                        showIcon={false}
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="followers" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Your Followers</h3>
                <p className="text-sm text-muted-foreground">
                  {connectionStats.follower_count} followers
                </p>
              </div>
              
              {connectionStats.follower_count === 0 ? (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No followers yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Share interesting posts and engage with the community to gain followers
                  </p>
                  <Button variant="outline" onClick={() => window.location.href = '/social'}>
                    Go to Social Feed
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Follower details coming soon. You have {connectionStats.follower_count} followers.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default Connections;
